server:
  port: 8082
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

spring:
  profiles:
    active: dev
  datasource:
    url: jdbc:h2:mem:testdb
    driverClassName: org.h2.Driver
    username: sa
    password: password
  h2:
    console:
      enabled: true
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: update
    show-sql: true

photo:
  upload:
    path: ${user.home}/photo-upload
    allowed-types: png,jpg,jpeg,gif
    max-size: 10485760 # 10MB
  cache:
    enabled: true
    max-size: 100 # 最大缓存文件数
    timeout: 3600 # 缓存过期时间（秒）

security:
  basic:
    enabled: true
    password: ${RANDOM_PASSWORD}

logging:
  level:
    com.example: DEBUG
    org.springframework.web: INFO
