package com.example.repository;

import com.example.entity.Photo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PhotoRepository extends JpaRepository<Photo, Long> {
    List<Photo> findByDeletedFalseAndPublicPhotoTrue();
    Optional<Photo> findByFileNameAndDeletedFalse(String fileName);
    List<Photo> findByUploadIpAndDeletedFalse(String ip);
    List<Photo> findByUploadTimeBetweenAndDeletedFalse(LocalDateTime start, LocalDateTime end);
}
